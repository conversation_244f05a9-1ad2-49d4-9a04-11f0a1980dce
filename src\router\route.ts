import { RouteRecordRaw } from 'vue-router';

/**
 * 建议：路由 path 路径与文件夹名称相同，找文件可浏览器地址找，方便定位文件位置
 *
 * 路由meta对象参数说明
 * meta: {
 *      title:          菜单栏及 tagsView 栏、菜单搜索名称（国际化）
 *      isLink：        是否超链接菜单，开启外链条件，`1、isLink: 链接地址不为空 2、isIframe:false`
 *      isHide：        是否隐藏此路由
 *      isKeepAlive：   是否缓存组件状态
 *      isAffix：       是否固定在 tagsView 栏上
 *      isIframe：      是否内嵌窗口，开启条件，`1、isIframe:true 2、isLink：链接地址不为空`
 *      roles：         当前路由权限标识，取角色管理。控制路由显示、隐藏。超级管理员：admin 普通角色：common
 *      icon：          菜单、tagsView 图标，阿里：加 `iconfont xxx`，fontawesome：加 `fa xxx`
 * }
 */

// 扩展 RouteMeta 接口
declare module 'vue-router' {
    interface RouteMeta {
        title?: string;
        isLink?: string;
        isHide?: boolean;
        isKeepAlive?: boolean;
        isAffix?: boolean;
        isIframe?: boolean;
        roles?: string[];
        icon?: string;
    }
}

/**
 * 定义动态路由
 * 前端添加路由，请在顶级节点的 `children 数组` 里添加
 * @description 未开启 isRequestRoutes 为 true 时使用（前端控制路由），开启时第一个顶级 children 的路由将被替换成接口请求回来的路由数据
 * @description 各字段请查看 `/@/views/system/menu/component/addMenu.vue 下的 ruleForm`
 * @returns 返回路由菜单数据
 */
export const dynamicRoutes: Array<RouteRecordRaw> = [
    {
        path: '/',
        name: '/',
        component: () => import('/@/layout/index.vue'),
        redirect: '/home',
        meta: {
            isKeepAlive: true,
        },
        children: [
            {
                path: '/home',
                name: 'home',
                component: () => import('/@/views/home/<USER>'),
                meta: {
                    title: '首页',
                    isLink: '',
                    isHide: false,
                    isKeepAlive: true,
                    isAffix: true,
                    isIframe: false,
                    roles: ['admin', 'common'],
                    icon: 'iconfont icon-shouye',
                },
            },

            {
                path: '/system',
                name: 'system',
                component: () => import('/@/layout/routerView/parent.vue'),
                redirect: '/system/menu',
                meta: {
                    title: '系统设置',
                    isLink: '',
                    isHide: false,
                    isKeepAlive: true,
                    isAffix: false,
                    isIframe: false,
                    roles: ['admin'],
                    icon: 'iconfont icon-xitongshezhi',
                },
                children: [
                    {
                        path: '/system/menu',
                        name: 'systemMenu',
                        component: () => import('/@/views/system/menu/index.vue'),
                        meta: {
                            title: '菜单管理',
                            isLink: '',
                            isHide: false,
                            isKeepAlive: true,
                            isAffix: false,
                            isIframe: false,
                            roles: ['admin'],
                            icon: 'iconfont icon-caidan',
                        },
                    },
                    {
                        path: '/system/role',
                        name: 'systemRole',
                        component: () => import('/@/views/system/role/index.vue'),
                        meta: {
                            title: '角色管理',
                            isLink: '',
                            isHide: false,
                            isKeepAlive: true,
                            isAffix: false,
                            isIframe: false,
                            roles: ['admin'],
                            icon: 'ele-ColdDrink',
                        },
                    },
                    {
                        path: '/system/user',
                        name: 'systemUser',
                        component: () => import('/@/views/system/user/index.vue'),
                        meta: {
                            title: '用户管理',
                            isLink: '',
                            isHide: false,
                            isKeepAlive: true,
                            isAffix: false,
                            isIframe: false,
                            roles: ['admin'],
                            icon: 'iconfont icon-icon-',
                        },
                    },
                    {
                        path: '/system/dept',
                        name: 'systemDept',
                        component: () => import('/@/views/system/dept/index.vue'),
                        meta: {
                            title: '部门管理',
                            isLink: '',
                            isHide: false,
                            isKeepAlive: true,
                            isAffix: false,
                            isIframe: false,
                            roles: ['admin'],
                            icon: 'ele-OfficeBuilding',
                        },
                    },
                    {
                        path: '/system/dic',
                        name: 'systemDic',
                        component: () => import('/@/views/system/dict/index.vue'),
                        meta: {
                            title: '字典管理',
                            isLink: '',
                            isHide: false,
                            isKeepAlive: true,
                            isAffix: false,
                            isIframe: false,
                            roles: ['admin'],
                            icon: 'ele-SetUp',
                        },
                    },
                ],
            },
            {
                path: '/ai',
                name: 'ai',
                component: () => import('/@/layout/routerView/parent.vue'),
                redirect: '/ai/kb',
                meta: {
                    title: 'AI管理',
                    isLink: '',
                    isHide: false,
                    isKeepAlive: true,
                    isAffix: false,
                    isIframe: false,
                    roles: ['admin'],
                    icon: 'iconfont icon-zhinenghua',
                },
                children: [
                    {
                        path: '/ai/kb/kbm',
                        name: 'aiKbKbm',
                        component: () => import('/@/views/ai/kb/kbm/index.vue'),
                        meta: {
                            title: '知识库管理',
                            isLink: '',
                            isHide: false,
                            isKeepAlive: true,
                            isAffix: false,
                            isIframe: false,
                            roles: ['admin'],
                            icon: 'iconfont icon-shujuku',
                        },
                    },
                    {
                        path: '/ai/kb/fm',
                        name: 'aiKbFm',
                        component: () => import('/@/views/ai/kb/fm/index.vue'),
                        meta: {
                            title: '文件管理',
                            isLink: '',
                            isHide: false,
                            isKeepAlive: true,
                            isAffix: false,
                            isIframe: false,
                            roles: ['admin'],
                            icon: 'iconfont icon-wenjian',
                        },
                    },
                    {
                        path: '/ai/kb/fv',
                        name: 'aiKbFv',
                        component: () => import('/@/views/ai/kb/fv/index.vue'),
                        meta: {
                            title: '文件向量化',
                            isLink: '',
                            isHide: false,
                            isKeepAlive: true,
                            isAffix: false,
                            isIframe: false,
                            roles: ['admin'],
                            icon: 'iconfont icon-xianglianghua',
                        },
                    },
                    {
                        path: '/ai/llm/chat',
                        name: 'aiLlmChat',
                        component: () => import('/@/views/ai/llm/chat/index.vue'),
                        meta: {
                            title: 'AI聊天',
                            isLink: '',
                            isHide: false,
                            isKeepAlive: true,
                            isAffix: false,
                            isIframe: false,
                            roles: ['admin'],
                            icon: 'iconfont icon-liaotian',
                        },
                    },
                ],
            },
        ],
    },
];

/**
 * 定义404、401界面
 * @link 参考：https://next.router.vuejs.org/zh/guide/essentials/history-mode.html#netlify
 */
export const notFoundAndNoPower = [
    {
        path: '/scada',
        component: () => import('/@/layout/routerView/parent.vue'),
        hidden: true,
        permissions: ['scada:echart:edit', 'scada:center:edit', 'scada:component:edit'],
        children: [
                    {
                        path: 'echart/detail',
                        component: () => import('/@/views/scada/echart/detail.vue'),
                        name: 'echartDetail',
                        meta: { title: '图表详情', activeMenu: '/scada/echart', noCache: true },
                    },
            {
                path: 'topo/editor',
                component: () => import('/@/views/scada/topo/editor.vue'),
                name: 'topoEditor',
                meta: { title: '组态详情', activeMenu: '/scada/topo', noCache: true },
            },
            {
                path: 'topo/fullscreen',
                component: () => import('/@/views/scada/topo/fullscreen.vue'),
                name: 'topoFullscreen',
                meta: { title: '预览详情', activeMenu: '/scada/topo', noCache: true },
            },
                    {
                        path: 'component/detail',
                        component: () => import('/@/views/scada/component/detail.vue'),
                        name: 'Editor',
                        meta: { title: '组件详情', activeMenu: '/scada/component', noCache: true },
                    },
        ],
    },
    {
        path: '/system/user-auth',
        // component: () => import('/@/views/system/user/authRole.vue'),
        hidden: true,
        permissions: ['system:user:edit'],
        children: [
            {
                path: 'role/:userId(\\d+)',
                component: () => import('/@/views/system/user/authRole.vue'),
                name: 'AuthRole',
                meta: { title: '分配角色', activeMenu: '/system/user' },
            },
        ],
    },
    {
        path: '/system/role-auth',
        // component: () => import('/@/views/system/role/authUser.vue'),
        hidden: true,
        permissions: ['system:role:edit'],
        children: [
            {
                path: 'user/:roleId(\\d+)',
                component: () => import('/@/views/system/role/authUser.vue'),
                name: 'AuthUser',
                meta: { title: '分配用户', activeMenu: '/system/role' },
            },
        ],
    },
    {
        path: '/system/dict-data',
        hidden: true,
        permissions: ['system:dict:list'],
        children: [
            {
                path: 'index/:dictId(\\d+)',
                component: () => import('/@/views/system/dict/data.vue'),
                name: 'Data',
                meta: { title: '字典数据', activeMenu: '/system/dict' },
            },
        ],
    },
    {
        path: '/monitor/job-log',
        // component: Layout,
        hidden: true,
        permissions: ['monitor:job:list'],
        children: [
            {
                path: 'index/:jobId(\\d+)',
                component: () => import('/@/views/monitor/job/log.vue'),
                name: 'JobLog',
                meta: { title: '调度日志', activeMenu: '/monitor/job' },
            },
        ],
    },
    // {
    //     path: '/tool/gen-edit',
    //     component: Layout,
    //     hidden: true,
    //     permissions: ['tool:gen:edit'],
    //     children: [
    //         {
    //             path: 'index/:tableId(\\d+)',
    //             component: () => import('@/views/tool/gen/editTable'),
    //             name: 'GenEdit',
    //             meta: { title: '修改生成配置', activeMenu: '/tool/gen' },
    //         },
    //     ],
    // },
    {
        path: '/iot',
        // component: () => import('/@/views/iot/product/index.vue'),
        hidden: true,
        permissions: ['iot:device:add', 'iot:device:query'],
        children: [
            {
                path: 'product-edit',
                component: () => import('/@/views/iot/product/product-edit.vue'),
                name: 'ProductEdit',
                meta: { title: '编辑产品', activeMenu: '/iot/product', nocache: true },
            },
            {
                path: 'device-edit',
                component: () => import('/@/views/iot/device/device-edit.vue'),
                name: 'DeviceEdit',
                meta: { title: '编辑设备', activeMenu: '/iot/device', noCache: true },
            }
            // {
            //     path: 'firmware-task',
            //     component: () => import('/@/views/iot/firmware/firmware-task'),
            //     name: 'FirmwareTask',
            //     meta: { title: '固件详情', activeMenu: '/iot/firmware' },
            // },
        ],
    },
    // {
    //     path: '/screen',
    //     // hidden: true,
    //     children: [
    //         {
    //             path: 'qqq',
    //             component: () => import('/@/views/chart/qqq.vue'),
    //             // hidden: false,
    //             name: 'Screen/chart',
    //             // meta: { title: '大屏展示', activeMenu: '/screen', nocache: true },
    //         }
    //     ],
    // },
    // {
    //     path: '/iot/varTemp-edit',
    //     component: Layout,
    //     hidden: true,
    //     permissions: ['iot:device:add'],
    //     children: [
    //         {
    //             path: 'point',
    //             component: () => import('@/views/iot/varTemp/point'),
    //             name: 'Point',
    //             meta: { title: '变量模板配置', activeMenu: '/iot/varTemp' },
    //         },
    //     ],
    // },

    // {
    //     path: '/iot/varTemp-edit',
    //     component: Layout,
    //     hidden: true,
    //     permissions: ['iot:device:add'],
    //     children: [
    //         {
    //             path: 'mjpoint',
    //             component: () => import('@/views/iot/varTemp/mjpoint'),
    //             name: 'mjPoint',
    //             meta: { title: '变量模板配置', activeMenu: '/iot/template' },
    //         },
    //     ],
    // },

    // {
    //     path: '/iot/varTemp-edit',
    //     component: Layout,
    //     hidden: true,
    //     permissions: ['iot:device:add'],
    //     children: [
    //         {
    //             path: 'gateway-things-model',
    //             component: () => import('@/views/iot/varTemp/gateway-things-model'),
    //             name: 'gateway-things-model',
    //             meta: { title: '网关采集点配置', activeMenu: '/iot/template' },
    //         },
    //     ],
    // },
    {
        path: '/:path(.*)*',
        name: 'notFound',
        component: () => import('/@/views/error/404.vue'),
        meta: {
            title: '找不到此页面',
            isHide: true,
        },
    },
    {
        path: '/401',
        name: 'noPower',
        component: () => import('/@/views/error/401.vue'),
        meta: {
            title: '没有权限',
            isHide: true,
        },
    },
];

/**
 * 定义静态路由（默认路由）
 * 此路由不要动，前端添加路由的话，请在 `dynamicRoutes 数组` 中添加
 * @description 前端控制直接改 dynamicRoutes 中的路由，后端控制不需要修改，请求接口路由数据时，会覆盖 dynamicRoutes 第一个顶级 children 的内容（全屏，不包含 layout 中的路由出口）
 * @returns 返回路由菜单数据
 */
export const staticRoutes: Array<RouteRecordRaw> = [
    {
        path: '/login',
        name: 'login',
        component: () => import('/@/views/login/index.vue'),
        meta: {
            title: '登录',
        },
    },
];
